// settingsConfig.js - Configuration for all setting types

export const settingConfigs = {
	// Mode Zone Settings
	'scoring': {
		type: 'select',
		label: 'Scoring Method',
		size: 'small',
		options: [
			{ value: 'count', label: 'Count' },
			{ value: 'hint', label: 'Hint' },
			{ value: 'speed', label: 'Speed' }
		],
		default: 'count'
	},
	'answering': {
		type: 'select',
		label: 'Answering Method',
		size: 'small',
		options: [
			{ value: 'typing', label: 'Typing' },
			{ value: 'mix', label: 'Mix' },
			{ value: 'multiple-choice', label: 'Multiple Choice' }
		],
		default: 'typing'
	},

	// General Zone Settings
	'players': {
		type: 'number',
		label: 'Number of Players',
		size: 'small',
		min: 1,
		max: 100,
		default: 8
	},
	'team-size': {
		type: 'number',
		label: 'Team Size',
		size: 'small',
		min: 1,
		max: 8,
		default: 1
	},
	'songs-and-types': {
		type: 'complex-songs-and-types',
		label: 'Songs & Types Selection',
		size: 'large',
		default: {
			songCount: { random: false, value: 20, min: 15, max: 25 },
			songTypes: {
				openings: { enabled: true, percentage: 50, count: 10, random: false, percentageMin: 40, percentageMax: 60, countMin: 8, countMax: 12 },
				endings: { enabled: true, percentage: 50, count: 10, random: false, percentageMin: 40, percentageMax: 60, countMin: 8, countMax: 12 },
				inserts: { enabled: false, percentage: 0, count: 0, random: false, percentageMin: 0, percentageMax: 0, countMin: 0, countMax: 0 }
			},
			songSelection: {
				random: { value: 0, randomRange: false, min: 0, max: 10 },
				watched: { value: 100, randomRange: false, min: 90, max: 100 }
			},
			mode: 'percentage'
		}
	},
	'watched-distribution': {
		type: 'select',
		label: 'Watched Distribution',
		size: 'small',
		options: [
			{ value: 'random', label: 'Random' },
			{ value: 'equal', label: 'Equal' }
		],
		default: 'random'
	},

	// Quiz Zone Settings
	'guess-time': {
		type: 'select',
		label: 'Guess Time',
		size: 'small',
		options: [
			{ value: 10, label: '10s' },
			{ value: 15, label: '15s' },
			{ value: 20, label: '20s' },
			{ value: 25, label: '25s' },
			{ value: 30, label: '30s' },
			{ value: 40, label: '40s' },
			{ value: 60, label: '60s' }
		],
		default: 20
	},
	'extra-time': {
		type: 'select',
		label: 'Extra Guess Time',
		size: 'small',
		options: [
			{ value: 0, label: '0s' },
			{ value: 1, label: '1s' },
			{ value: 2, label: '2s' },
			{ value: 3, label: '3s' },
			{ value: 4, label: '4s' },
			{ value: 5, label: '5s' }
		],
		default: 0
	},
	'sample-point': {
		type: 'select',
		label: 'Sample Point',
		size: 'small',
		options: [
			{ value: 20, label: '20%' },
			{ value: 30, label: '30%' },
			{ value: 40, label: '40%' },
			{ value: 50, label: '50%' },
			{ value: 60, label: '60%' },
			{ value: 70, label: '70%' },
			{ value: 80, label: '80%' }
		],
		default: 50
	},
	'playback-speed': {
		type: 'select',
		label: 'Playback Speed',
		size: 'small',
		options: [
			{ value: 1, label: '1x' },
			{ value: 1.5, label: '1.5x' },
			{ value: 2, label: '2x' },
			{ value: 4, label: '4x' }
		],
		default: 1
	},
	'modifiers': {
		type: 'select',
		label: 'Modifiers',
		size: 'small',
		options: [
			{ value: 'none', label: 'None' },
			{ value: 'skip-guessing', label: 'Skip Guessing' },
			{ value: 'skip-results', label: 'Skip Results' },
			{ value: 'queueing', label: 'Queueing' },
			{ value: 'all', label: 'All Modifiers' }
		],
		default: 'all'
	},

	// Anime Zone Settings
	'anime-type': {
		type: 'checkboxes',
		label: 'Anime Types',
		size: 'small',
		options: [
			{ key: 'tv', label: 'TV', default: true },
			{ key: 'movie', label: 'Movie', default: true },
			{ key: 'ova', label: 'OVA', default: true },
			{ key: 'ona', label: 'ONA', default: true },
			{ key: 'special', label: 'Special', default: true }
		]
	},
	

	'song-difficulty': {
		type: 'complex-song-difficulty',
		label: 'Song Difficulty',
		size: 'large',
		default: {
			easy: { enabled: true, percentage: 33, count: 7, randomRange: false, minPercentage: 25, maxPercentage: 40, minCount: 5, maxCount: 10 },
			medium: { enabled: true, percentage: 33, count: 7, randomRange: false, minPercentage: 25, maxPercentage: 40, minCount: 5, maxCount: 10 },
			hard: { enabled: true, percentage: 34, count: 6, randomRange: false, minPercentage: 25, maxPercentage: 40, minCount: 4, maxCount: 8 },
			mode: 'percentage',
			viewMode: 'basic',
			ranges: []
		}
	},
	'player-score': {
		type: 'complex-score-range',
		label: 'Player Score',
		size: 'fullscreen',
		min: 1,
		max: 10,
		default: { min: 1, max: 10, mode: 'range', percentages: {} }
	},
	'anime-score': {
		type: 'complex-score-range',
		label: 'Anime Score',
		size: 'fullscreen',
		min: 2,
		max: 10,
		default: { min: 2, max: 10, mode: 'range', percentages: {} }
	},
	'vintage': {
		type: 'complex-vintage',
		label: 'Vintage',
		size: 'fullscreen',
		default: { ranges: [{ from: { season: 'Winter', year: 1944 }, to: { season: 'Fall', year: 2025 } }] }
	},
	'genres': {
		type: 'complex-genres-tags',
		label: 'Genres',
		size: 'fullscreen',
		default: { mode: 'basic', included: [], excluded: [], optional: [], percentages: {}, counts: {} }
	},
	'tags': {
		type: 'complex-genres-tags',
		label: 'Tags',
		size: 'fullscreen',
		default: { mode: 'basic', included: [], excluded: [], optional: [], percentages: {}, counts: {} }
	},
	'song-categories': {
		type: 'song-categories',
		label: 'Song Categories',
		size: 'medium',
		default: {
			standard: { openings: true, endings: true, inserts: true },
			instrumental: { openings: true, endings: true, inserts: true },
			chanting: { openings: true, endings: true, inserts: true },
			character: { openings: true, endings: true, inserts: true }
		}
	},
	'number-of-songs': {
		type: 'number-with-random',
		label: 'Number of Songs',
		size: 'medium',
		min: 1,
		max: 100,
		default: { random: false, value: 20, min: 15, max: 25 },
		allowRandom: true
	},
	'song-categories': {
		type: 'song-categories',
		label: 'Song Categories',
		size: 'medium',
		default: {
			standard: { openings: true, endings: true, inserts: true },
			instrumental: { openings: true, endings: true, inserts: true },
			chanting: { openings: true, endings: true, inserts: true },
			character: { openings: true, endings: true, inserts: true }
		}
	}
};
