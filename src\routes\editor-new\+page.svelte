<script>
	import { SvelteFlow, Controls, Background, MiniMap } from '@xyflow/svelte';
	import '@xyflow/svelte/dist/style.css';
	import { Button } from '$lib/components/ui/button';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import ConfirmationModal from '$lib/components/ui/confirmation-modal.svelte';
	import { closeAllModals } from '$lib/stores/modalStore.js';
	import NodeGroupComponent from '$lib/components/amqplus/NodeGroupComponent.svelte';

	import '$lib/styles/amqplus.css';

	// Define node types
	const nodeTypes = {
		filterNode: NodeGroupComponent
	};

	// Available Filter Nodes - All nodes that can be added to filter the song collection
	const availableFilterNodes = {
		// Basic Settings - Singular node that cannot be deleted (starting point)
		'basic-settings': {
			title: 'Basic Settings',
			description: 'Core quiz configuration including scoring, answering, players, and timing',
			color: '#75B9DF', // Blue
			icon: '⚙️',
			allowMultiple: false,
			deletable: false,
			category: 'basic',
			nodes: [
				{ id: 'scoring', title: 'Scoring', icon: '🏆', defaultValue: 'count' },
				{ id: 'answering', title: 'Answering', icon: '⌨️', defaultValue: 'typing' },
				{ id: 'players', title: 'Players', icon: '👥', defaultValue: 8 },
				{ id: 'team-size', title: 'Team Size', icon: '👫', defaultValue: 1 },
				{ id: 'guess-time', title: 'Guess Time', icon: '⏰', defaultValue: 20 },
				{ id: 'extra-time', title: 'Extra Guess Time', icon: '⏳', defaultValue: 0 },
				{ id: 'sample-point', title: 'Sample Point', icon: '🎯', defaultValue: { useRange: true, start: 20, end: 80, staticValue: 50 } },
				{ id: 'playback-speed', title: 'Playback Speed', icon: '⚡', defaultValue: 1 },
				{ id: 'modifiers', title: 'Modifiers', icon: '🔧', defaultValue: { skipGuessing: true, skipResults: true, queueing: true } }
			]
		},

		// Filter Nodes - Individual filtering steps
		'songs-and-types': {
			title: 'Songs & Types',
			description: 'Filter by song types (openings, endings, inserts) and categories',
			color: '#10B981', // Green
			icon: '🎵',
			allowMultiple: true,
			deletable: true,
			category: 'filter',
			defaultValue: {
				songTypes: {
					openings: { enabled: true, percentage: 50, count: 10, random: false, percentageMin: 40, percentageMax: 60, countMin: 8, countMax: 12 },
					endings: { enabled: true, percentage: 50, count: 10, random: false, percentageMin: 40, percentageMax: 60, countMin: 8, countMax: 12 },
					inserts: { enabled: false, percentage: 0, count: 0, random: false, percentageMin: 0, percentageMax: 0, countMin: 0, countMax: 0 }
				},
				songSelection: {
					random: { value: 0, randomRange: false, min: 0, max: 10 },
					watched: { value: 100, randomRange: false, min: 90, max: 100 }
				},
				mode: 'percentage'
			}
		},

		'song-categories': {
			title: 'Song Categories',
			description: 'Filter by song categories: Standard, Instrumental, Chanting, Character',
			color: '#8B5CF6', // Purple
			icon: '🎼',
			allowMultiple: true,
			deletable: true,
			category: 'filter',
			defaultValue: {
				standard: { openings: true, endings: true, inserts: true },
				instrumental: { openings: true, endings: true, inserts: true },
				chanting: { openings: true, endings: true, inserts: true },
				character: { openings: true, endings: true, inserts: true }
			}
		},

		'anime-type': {
			title: 'Anime Type',
			description: 'Filter by anime format: TV, Movie, OVA, ONA, Special',
			color: '#F59E0B', // Amber
			icon: '🎬',
			allowMultiple: true,
			deletable: true,
			category: 'filter',
			defaultValue: { tv: true, movie: true, ova: true, ona: true, special: true }
		},

		'vintage': {
			title: 'Vintage',
			description: 'Filter by anime release date ranges and seasons',
			color: '#EF4444', // Red
			icon: '📅',
			allowMultiple: true,
			deletable: true,
			category: 'filter',
			defaultValue: { ranges: [{ from: { season: 'Winter', year: 1944 }, to: { season: 'Fall', year: 2025 } }] }
		},

		'song-difficulty': {
			title: 'Song Difficulty',
			description: 'Filter by song difficulty levels: Easy, Medium, Hard',
			color: '#EC4899', // Pink
			icon: '📈',
			allowMultiple: true,
			deletable: true,
			category: 'filter',
			defaultValue: {
				easy: { enabled: true, percentage: 33, count: 7, randomRange: false, minPercentage: 25, maxPercentage: 40, minCount: 5, maxCount: 10 },
				medium: { enabled: true, percentage: 33, count: 7, randomRange: false, minPercentage: 25, maxPercentage: 40, minCount: 5, maxCount: 10 },
				hard: { enabled: true, percentage: 34, count: 6, randomRange: false, minPercentage: 25, maxPercentage: 40, minCount: 4, maxCount: 8 },
				mode: 'percentage',
				viewMode: 'basic',
				ranges: []
			}
		},

		'player-score': {
			title: 'Player Score',
			description: 'Filter by player score ranges and percentages',
			color: '#06B6D4', // Cyan
			icon: '👤',
			allowMultiple: true,
			deletable: true,
			category: 'filter',
			defaultValue: { min: 1, max: 10, mode: 'range', percentages: {} }
		},

		'anime-score': {
			title: 'Anime Score',
			description: 'Filter by anime rating scores and ranges',
			color: '#84CC16', // Lime
			icon: '⭐',
			allowMultiple: true,
			deletable: true,
			category: 'filter',
			defaultValue: { min: 2, max: 10, mode: 'range', percentages: {} }
		},

		'number-of-songs': {
			title: 'Number of Songs',
			description: 'Select final number of songs for the quiz (should be last node)',
			color: '#DC2626', // Red
			icon: '🔢',
			allowMultiple: false,
			deletable: false, // Cannot be deleted
			category: 'final',
			defaultValue: { random: false, value: 20, min: 15, max: 25 }
		}
	};

	// Node counter for unique IDs
	let nodeCounter = 1;

	// Generate unique node ID
	function generateNodeId(groupType, nodeType) {
		return `${groupType}-${nodeType}-${nodeCounter++}`;
	}

	// Create initial nodes - Basic Settings (required) + example filtering pipeline
	function createInitialNodes() {
		const nodes = [];

		// Create the required Basic Settings node
		const basicSettingsNode = createFilterNode('basic-settings', 'basic-settings-1', { x: 100, y: 200 });

		// Initialize with all basic settings
		const basicNodeDef = availableFilterNodes['basic-settings'];
		basicNodeDef.nodes.forEach(node => {
			basicSettingsNode.data.settings[node.id] = node.defaultValue;
		});

		nodes.push(basicSettingsNode);

		// Create example filtering pipeline - horizontally spaced
		const songsTypesNode = createFilterNode('songs-and-types', 'songs-and-types-1', { x: 450, y: 200 });
		songsTypesNode.data.settings['songs-and-types'] = availableFilterNodes['songs-and-types'].defaultValue;
		nodes.push(songsTypesNode);

		const songCategoriesNode = createFilterNode('song-categories', 'song-categories-1', { x: 800, y: 200 });
		songCategoriesNode.data.settings['song-categories'] = availableFilterNodes['song-categories'].defaultValue;
		nodes.push(songCategoriesNode);

		const animeTypeNode = createFilterNode('anime-type', 'anime-type-1', { x: 1150, y: 200 });
		animeTypeNode.data.settings['anime-type'] = availableFilterNodes['anime-type'].defaultValue;
		nodes.push(animeTypeNode);

		const songDifficultyNode = createFilterNode('song-difficulty', 'song-difficulty-1', { x: 1500, y: 200 });
		songDifficultyNode.data.settings['song-difficulty'] = availableFilterNodes['song-difficulty'].defaultValue;
		nodes.push(songDifficultyNode);

		const numberOfSongsNode = createFilterNode('number-of-songs', 'number-of-songs-1', { x: 1850, y: 200 });
		numberOfSongsNode.data.settings['number-of-songs'] = availableFilterNodes['number-of-songs'].defaultValue;
		nodes.push(numberOfSongsNode);

		return nodes;
	}

	// Helper function to create a filter node
	function createFilterNode(nodeType, nodeId, position) {
		const nodeDef = availableFilterNodes[nodeType];
		return {
			id: nodeId,
			type: 'filterNode',
			position,
			data: {
				id: nodeId,
				nodeType,
				title: nodeDef.title,
				description: nodeDef.description,
				color: nodeDef.color,
				icon: nodeDef.icon,
				deletable: nodeDef.deletable,
				allowMultiple: nodeDef.allowMultiple,
				category: nodeDef.category,
				settings: {},
				onNodeAdd: handleNodeAdd,
				onNodeRemove: handleNodeRemove,
				onNodeEdit: handleNodeEdit,
				onGroupDelete: handleGroupDelete
			},
			deletable: nodeDef.deletable,
			draggable: true
		};
	}

	// Create initial edges showing the filtering pipeline
	function createInitialEdges() {
		return [
			{
				id: 'basic-to-songs-types',
				source: 'basic-settings-1',
				target: 'songs-and-types-1',
				type: 'step',
				style: 'stroke: #10B981; stroke-width: 3; stroke-dasharray: 8,4;',
				animated: true
			},
			{
				id: 'songs-types-to-categories',
				source: 'songs-and-types-1',
				target: 'song-categories-1',
				type: 'step',
				style: 'stroke: #8B5CF6; stroke-width: 3; stroke-dasharray: 8,4;',
				animated: true
			},
			{
				id: 'categories-to-anime-type',
				source: 'song-categories-1',
				target: 'anime-type-1',
				type: 'step',
				style: 'stroke: #F59E0B; stroke-width: 3; stroke-dasharray: 8,4;',
				animated: true
			},
			{
				id: 'anime-type-to-difficulty',
				source: 'anime-type-1',
				target: 'song-difficulty-1',
				type: 'step',
				style: 'stroke: #EC4899; stroke-width: 3; stroke-dasharray: 8,4;',
				animated: true
			},
			{
				id: 'difficulty-to-number',
				source: 'song-difficulty-1',
				target: 'number-of-songs-1',
				type: 'step',
				style: 'stroke: #DC2626; stroke-width: 3; stroke-dasharray: 8,4;',
				animated: true
			}
		];
	}

	// Initialize nodes and edges
	let nodes = $state(createInitialNodes());
	let edges = $state(createInitialEdges());

	// Validation function
	function validateConfiguration() {
		// Check if Basic Settings node exists
		const basicSettingsExists = nodes.some(node => 
			node.data.groupType === 'basicSettings'
		);
		
		if (!basicSettingsExists) {
			return {
				valid: false,
				message: 'Basic Settings node is required and cannot be deleted.'
			};
		}
		
		// Check if Basic Settings is connected
		const basicSettingsNode = nodes.find(node => node.data.groupType === 'basicSettings');
		const hasOutgoingConnection = edges.some(edge => edge.source === basicSettingsNode.id);
		
		if (!hasOutgoingConnection) {
			return {
				valid: false,
				message: 'Basic Settings node must be connected to at least one other node.'
			};
		}
		
		return { valid: true, message: '' };
	}

	// Event handlers
	const onNodeClick = (event) => {
		console.log('Node clicked:', event.targetNode);
	};

	const onEdgeClick = (event) => {
		console.log('Edge clicked:', event.targetEdge);
	};

	// Save configuration
	function saveConfiguration() {
		const validation = validateConfiguration();
		if (!validation.valid) {
			alert(validation.message);
			return;
		}
		
		console.log('Configuration saved:', { nodes, edges });
		alert('Configuration saved successfully!');
	}

	// Reset to default template
	function resetToDefault() {
		nodes = createInitialNodes();
		edges = createInitialEdges();
		nodeCounter = 7; // Reset counter (6 initial nodes + 1)
	}

	// Add new filter node
	function addFilterNode(nodeType) {
		const nodeDef = availableFilterNodes[nodeType];
		if (!nodeDef) return;

		// Check if it's Basic Settings and already exists
		if (nodeType === 'basic-settings' && !nodeDef.allowMultiple) {
			const exists = nodes.some(node => node.data.nodeType === 'basic-settings');
			if (exists) {
				alert('Basic Settings node already exists and only one is allowed.');
				return;
			}
		}

		// Check if it's Number of Songs and already exists
		if (nodeType === 'number-of-songs') {
			const exists = nodes.some(node => node.data.nodeType === 'number-of-songs');
			if (exists) {
				alert('Number of Songs node already exists and only one is allowed.');
				return;
			}
		}

		const nodeId = generateNodeId(nodeType, 'filter');
		// Place nodes horizontally, 350px apart
		const position = { x: 100 + (nodes.length * 350), y: 200 };
		const newNode = createFilterNode(nodeType, nodeId, position);

		// Initialize with default value if it exists
		if (nodeDef.defaultValue) {
			newNode.data.settings[nodeType] = nodeDef.defaultValue;
		}

		nodes = [...nodes, newNode];
	}

	// Sidebar state
	let sidebarOpen = $state(false);

	// Node group event handlers
	function handleNodeAdd(groupId, nodeId) {
		const groupNode = nodes.find(n => n.id === groupId);
		if (!groupNode) return;

		const groupDef = nodeGroupDefinitions[groupNode.data.groupType];
		const nodeDef = groupDef.nodes.find(n => n.id === nodeId);
		if (!nodeDef) return;

		// Add the node to the group's settings with default value
		groupNode.data.settings[nodeId] = nodeDef.defaultValue;

		// Trigger reactivity
		nodes = [...nodes];
	}

	function handleNodeRemove(groupId, nodeId) {
		const groupNode = nodes.find(n => n.id === groupId);
		if (!groupNode) return;

		// Remove the node from the group's settings
		delete groupNode.data.settings[nodeId];

		// Trigger reactivity
		nodes = [...nodes];
	}

	function handleNodeEdit(groupId, nodeId, newValue) {
		console.log('handleNodeEdit called:', { groupId, nodeId, newValue });
		const groupNode = nodes.find(n => n.id === groupId);
		if (!groupNode) {
			console.warn('Group node not found:', groupId);
			return;
		}

		// Update the node's value
		groupNode.data.settings[nodeId] = newValue;
		console.log('Updated settings:', groupNode.data.settings);

		// Force reactivity by creating new nodes array
		nodes = nodes.map(node =>
			node.id === groupId
				? { ...node, data: { ...node.data, settings: { ...node.data.settings } } }
				: node
		);
	}

	function handleGroupDelete(groupId) {
		const groupNode = nodes.find(n => n.id === groupId);
		if (!groupNode || !groupNode.data.deletable) return;

		// Remove the node
		nodes = nodes.filter(n => n.id !== groupId);

		// Remove related edges
		edges = edges.filter(edge => edge.source !== groupId && edge.target !== groupId);
	}
</script>

<!-- Node Editor -->
<main class="relative w-full h-screen">
	<!-- Top toolbar -->
	<div class="absolute top-4 left-4 z-20 flex gap-2">
		<Button onclick={saveConfiguration} class="bg-green-600 hover:bg-green-700">
			Save Configuration
		</Button>
		<Button onclick={resetToDefault} variant="outline">
			Reset to Default
		</Button>
		<Button onclick={() => sidebarOpen = !sidebarOpen} variant="outline">
			{sidebarOpen ? 'Close' : 'Add Nodes'}
		</Button>
	</div>

	<!-- Info panel -->
	<div class="absolute top-4 right-4 z-20 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg max-w-sm">
		<h4 class="font-semibold text-sm mb-2">Filtering Pipeline</h4>
		<p class="text-xs text-gray-600 mb-3">Start with all songs, then filter step by step</p>
		<div class="text-xs space-y-1">
			<div class="flex items-center gap-2">
				<div class="w-3 h-3 rounded" style="background: #75B9DF;"></div>
				<span>Basic Settings (Required start)</span>
			</div>
			<div class="flex items-center gap-2">
				<div class="w-3 h-3 rounded" style="background: #10B981;"></div>
				<span>Filter nodes (Order matters)</span>
			</div>
			<div class="flex items-center gap-2">
				<div class="w-3 h-3 rounded" style="background: #DC2626;"></div>
				<span>Number of Songs (Final step)</span>
			</div>
		</div>
	</div>

	<!-- Sidebar -->
	{#if sidebarOpen}
		<div class="absolute top-0 right-0 h-full w-80 bg-white border-l border-gray-200 shadow-lg z-30 overflow-y-auto">
			<div class="p-4">
				<h3 class="text-lg font-semibold mb-4">Add Filter Nodes</h3>
				<p class="text-sm text-gray-600 mb-4">Build your filtering pipeline by adding nodes that process the song collection.</p>

				<div class="space-y-3">
					{#each Object.entries(availableFilterNodes) as [nodeType, nodeDef]}
						<Card class="cursor-pointer hover:shadow-md transition-shadow" onclick={() => addFilterNode(nodeType)}>
							<CardContent class="p-4">
								<div class="flex items-center gap-3">
									<span class="text-2xl">{nodeDef.icon}</span>
									<div class="flex-1">
										<h4 class="font-medium" style="color: {nodeDef.color};">{nodeDef.title}</h4>
										<p class="text-xs text-gray-600 mt-1">{nodeDef.description}</p>
										<div class="mt-2 flex gap-1">
											{#if nodeDef.category === 'basic'}
												<span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Required</span>
											{:else if nodeDef.category === 'final'}
												<span class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">Final Step</span>
											{:else}
												<span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Filter</span>
											{/if}
											{#if nodeDef.allowMultiple}
												<span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">Multiple</span>
											{/if}
										</div>
									</div>
								</div>
							</CardContent>
						</Card>
					{/each}
				</div>
			</div>
		</div>
	{/if}

	<div class="w-full h-full">
		<SvelteFlow
			{nodes}
			{edges}
			{nodeTypes}
			onnodeclick={onNodeClick}
			onedgeclick={onEdgeClick}
			proOptions={{ hideAttribution: true }}
			nodesDraggable={true}
			nodesConnectable={true}
			elementsSelectable={true}
			zoomOnDoubleClick={false}
		>
			<Background variant="dots" gap={20} size={1} color="rgba(223, 105, 117, 0.1)" />
			<Controls />
		</SvelteFlow>
	</div>
</main>
