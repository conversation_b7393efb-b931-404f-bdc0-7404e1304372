<script>
	import { <PERSON><PERSON>, Position } from '@xyflow/svelte';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import { Plus, Settings, Trash2, ChevronDown } from 'lucide-svelte';
	import { settingConfigs } from '$lib/components/amqplus/dialog/settingsConfig.js';
	import NodeEditDialog from '$lib/components/amqplus/NodeEditDialog.svelte';

	let { data } = $props();

	// Extract properties from data
	const {
		nodeType,
		title,
		description,
		color,
		icon,
		deletable = true,
		allowMultiple = true,
		category = 'filter',
		settings = {},
		onNodeAdd,
		onNodeRemove,
		onNodeEdit,
		onGroupDelete
	} = data;

	// For single-node filters, we show the node type as the main setting
	// For basic settings, we show all available sub-settings
	const availableNodes = $derived(() => {
		if (nodeType === 'basic-settings') {
			// Return all basic settings as individual configurable items
			return [
				{ id: 'scoring', title: 'Scoring', icon: '🏆' },
				{ id: 'answering', title: 'Answering', icon: '⌨️' },
				{ id: 'players', title: 'Players', icon: '👥' },
				{ id: 'team-size', title: 'Team Size', icon: '👫' },
				{ id: 'guess-time', title: 'Guess Time', icon: '⏰' },
				{ id: 'extra-time', title: 'Extra Guess Time', icon: '⏳' },
				{ id: 'sample-point', title: 'Sample Point', icon: '🎯' },
				{ id: 'playback-speed', title: 'Playback Speed', icon: '⚡' },
				{ id: 'modifiers', title: 'Modifiers', icon: '🔧' }
			];
		} else {
			// For filter nodes, return the single main setting
			return [{ id: nodeType, title: title, icon: icon }];
		}
	});

	// Get nodes that are not yet added to this group
	const availableToAdd = $derived(() => {
		const nodes = availableNodes();
		return nodes.filter(node => !settings[node.id]);
	});

	// Get nodes that are currently in this group
	const currentNodes = $derived(() => {
		const nodes = availableNodes();
		return nodes.filter(node => settings[node.id]);
	});

	// Color utilities
	function hexToRgba(hex, alpha) {
		const r = parseInt(hex.slice(1, 3), 16);
		const g = parseInt(hex.slice(3, 5), 16);
		const b = parseInt(hex.slice(5, 7), 16);
		return `rgba(${r}, ${g}, ${b}, ${alpha})`;
	}

	const backgroundColor = $derived(hexToRgba(color, 0.1));
	const borderColor = $derived(hexToRgba(color, 0.6));

	// Dialog state
	let dialogOpen = $state(false);
	let nodeDataForDialog = $state(null);

	// Add node dropdown state
	let addNodeDropdownOpen = $state(false);

	// Event handlers
	function handleAddNode(nodeId) {
		if (onNodeAdd) {
			onNodeAdd(data.id, nodeId);
		}
		addNodeDropdownOpen = false;
	}

	function handleRemoveNode(nodeId) {
		if (onNodeRemove) {
			onNodeRemove(data.id, nodeId);
		}
	}

	function handleEditNode(nodeId) {
		const nodes = availableNodes();
		const nodeConfig = nodes.find(n => n.id === nodeId);
		if (!nodeConfig) return;

		nodeDataForDialog = {
			id: nodeId,
			title: nodeConfig.title,
			icon: nodeConfig.icon,
			color: color,
			currentValue: settings[nodeId]
		};
		dialogOpen = true;
	}

	function handleDeleteGroup() {
		if (!deletable) return;
		if (onGroupDelete) {
			onGroupDelete(data.id);
		}
	}

	function handleSaveNode(saveData) {
		if (onNodeEdit) {
			onNodeEdit(data.id, saveData.nodeId, saveData.newValue);
		}
		dialogOpen = false;
	}

	function handleModalClose() {
		dialogOpen = false;
	}

	// Close dropdown when clicking outside
	function handleClickOutside(event) {
		if (addNodeDropdownOpen && !event.target.closest('.add-node-dropdown')) {
			addNodeDropdownOpen = false;
		}
	}

	// Add event listener for click outside
	$effect(() => {
		if (addNodeDropdownOpen) {
			document.addEventListener('click', handleClickOutside);
			return () => {
				document.removeEventListener('click', handleClickOutside);
			};
		}
	});

	// Format display value for nodes with more detailed information
	function formatDisplayValue(value, nodeId) {
		if (value === null || value === undefined) return 'Not configured';

		const config = settingConfigs[nodeId];
		if (!config) return String(value);

		switch (config.type) {
			case 'select':
				const option = config.options?.find(opt => opt.value === value);
				return option ? option.label : String(value);
			case 'number':
				return String(value);
			case 'number-with-random':
				if (typeof value === 'object') {
					if (value.random) {
						return `Random: ${value.min}-${value.max}`;
					} else {
						return `Fixed: ${value.value}`;
					}
				}
				return String(value);
			case 'checkboxes':
				if (typeof value === 'object') {
					const enabled = Object.entries(value).filter(([_, enabled]) => enabled).map(([key, _]) => key);
					return enabled.length > 0 ? `${enabled.join(', ')}` : 'None enabled';
				}
				return String(value);
			case 'song-categories':
				if (typeof value === 'object') {
					const totalEnabled = Object.values(value).reduce((sum, category) => {
						if (typeof category === 'object') {
							return sum + Object.values(category).filter(Boolean).length;
						}
						return sum;
					}, 0);

					// Show compact summary for long text
					const enabledCategories = Object.entries(value)
						.filter(([_, types]) => typeof types === 'object' && Object.values(types).some(Boolean))
						.map(([category, _]) => category);

					if (enabledCategories.length === 0) return 'No categories enabled';
					if (enabledCategories.length === 4) return `All categories (${totalEnabled}/12 types)`;

					return `${enabledCategories.join(', ')} (${totalEnabled}/12 types)`;
				}
				return String(value);
			case 'complex-songs-and-types':
				if (typeof value === 'object' && value.songTypes) {
					const enabled = Object.entries(value.songTypes).filter(([_, type]) => type.enabled);
					const details = enabled.map(([key, type]) => {
						if (value.mode === 'percentage') {
							return `${key}: ${type.percentage}%`;
						} else {
							return `${key}: ${type.count}`;
						}
					});
					return details.join(', ') + ` (${value.mode})`;
				}
				return 'Songs & Types configured';
			case 'complex-song-difficulty':
				if (typeof value === 'object') {
					const enabled = Object.entries(value).filter(([key, diff]) =>
						key !== 'mode' && key !== 'viewMode' && key !== 'ranges' && diff.enabled
					);
					const details = enabled.map(([key, diff]) => {
						if (value.mode === 'percentage') {
							return `${key}: ${diff.percentage}%`;
						} else {
							return `${key}: ${diff.count}`;
						}
					});
					return details.join(', ') + ` (${value.mode})`;
				}
				return 'Difficulty configured';
			case 'complex-vintage':
				if (typeof value === 'object' && value.ranges) {
					const rangeDetails = value.ranges.map(range =>
						`${range.from.season} ${range.from.year} - ${range.to.season} ${range.to.year}`
					);
					return rangeDetails.join(', ');
				}
				return 'Vintage configured';
			case 'complex-player-anime-score':
				if (typeof value === 'object') {
					if (value.mode === 'range') {
						return `Range: ${value.min}-${value.max}`;
					} else {
						const percentages = Object.entries(value.percentages || {})
							.filter(([_, percent]) => percent > 0)
							.map(([score, percent]) => `${score}: ${percent}%`);
						return percentages.length > 0 ? percentages.join(', ') : 'Percentage mode';
					}
				}
				return 'Score configured';
			case 'sample-point-with-static':
			case 'complex-sample-point':
				if (typeof value === 'object') {
					if (value.useRange) {
						return `Range: ${value.start}%-${value.end}%`;
					} else {
						return `Fixed: ${value.staticValue}%`;
					}
				}
				return 'Sample point configured';
			default:
				if (typeof value === 'object') {
					return 'Configured';
				}
				return String(value);
		}
	}

	// Get a summary of all settings in this node
	function getNodeSummary() {
		const currentNodesArray = currentNodes();
		if (currentNodesArray.length === 0) {
			return nodeType === 'basic-settings' ? 'Click + to add basic settings' : 'No configuration yet';
		}

		const summaries = currentNodesArray.map(node => {
			const value = settings[node.id];
			const displayValue = formatDisplayValue(value, node.id);
			return `${node.icon} ${displayValue}`;
		});

		return summaries.join(' • ');
	}
</script>

<div
	class="relative node-group-component min-w-80 max-w-96"
	style="background: {backgroundColor}; border: 3px solid {borderColor}; border-radius: 16px;"
>
	<!-- Input handle (except for Basic Settings which is the start) -->
	{#if nodeType !== 'basic-settings'}
		<Handle
			type="target"
			position={Position.Left}
			style="width: 16px; height: 16px; background: {color}; border: 3px solid white;"
		/>
	{/if}

	<!-- Output handle (all groups can connect to others) -->
	<Handle
		type="source"
		position={Position.Right}
		style="width: 16px; height: 16px; background: {color}; border: 3px solid white;"
	/>

	<Card class="border-0 bg-transparent shadow-none">
		<CardHeader class="pb-3">
			<div class="flex items-center justify-between">
				<div class="flex items-center gap-2 flex-1 min-w-0">
					<span class="text-2xl flex-shrink-0">{icon}</span>
					<div class="flex-1 min-w-0">
						<CardTitle class="text-lg font-bold" style="color: {color};">
							{title}
						</CardTitle>
						<p class="text-sm text-gray-600 mt-1">{description}</p>
					</div>
				</div>
				
				<div class="flex items-center gap-1 relative">
					<!-- Add Node Dropdown -->
					{#if availableToAdd().length > 0}
						<div class="relative add-node-dropdown">
							<Button
								size="sm"
								variant="outline"
								class="h-8 w-8 p-0 border-2 hover:bg-gray-100"
								onclick={() => addNodeDropdownOpen = !addNodeDropdownOpen}
							>
								<Plus class="h-4 w-4" />
							</Button>

							{#if addNodeDropdownOpen}
								<div class="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 min-w-48">
									<div class="p-2 border-b border-gray-100 text-sm font-medium text-gray-700">
										Add Node
									</div>
									{#each availableToAdd() as node}
										<button
											class="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 flex items-center gap-2"
											onclick={() => handleAddNode(node.id)}
										>
											<span>{node.icon}</span>
											{node.title}
										</button>
									{/each}
								</div>
							{/if}
						</div>
					{/if}

					<!-- Delete Group Button -->
					{#if deletable && nodeType !== 'basic-settings' && nodeType !== 'number-of-songs'}
						<Button
							size="sm"
							variant="outline"
							class="h-8 w-8 p-0 text-red-600 border-red-300 hover:bg-red-50 hover:border-red-400"
							onclick={handleDeleteGroup}
						>
							<Trash2 class="h-4 w-4" />
						</Button>
					{/if}
				</div>
			</div>
		</CardHeader>

		<CardContent class="pt-0">
			<!-- Current Nodes -->
			{#if currentNodes().length > 0}
				<div class="space-y-2">
					{#each currentNodes() as node}
						<div class="flex flex-col p-2 rounded-lg bg-white/50">
							<div class="flex items-center justify-between">
								<div class="flex items-center gap-2 flex-1">
									<span class="text-sm">{node.icon}</span>
									<div class="flex-1">
										<div class="text-sm font-medium break-words">
											{formatDisplayValue(settings[node.id], node.id)}
										</div>
									</div>
								</div>
								<div class="flex items-center gap-1">
									<Button
										size="sm"
										variant="outline"
										class="h-6 w-6 p-0 border-blue-300 text-blue-600 hover:bg-blue-50"
										onclick={() => handleEditNode(node.id)}
									>
										<Settings class="h-3 w-3" />
									</Button>
									{#if nodeType !== 'basic-settings' && nodeType !== 'number-of-songs'}
										<Button
											size="sm"
											variant="outline"
											class="h-6 w-6 p-0 border-red-300 text-red-600 hover:bg-red-50"
											onclick={() => handleRemoveNode(node.id)}
										>
											<Trash2 class="h-3 w-3" />
										</Button>
									{/if}
								</div>
							</div>
						</div>
					{/each}
				</div>
			{:else}
				<div class="text-center py-4 text-gray-500 text-sm">
					{nodeType === 'basic-settings' ? 'Click + to add basic settings' : 'No configuration yet'}
				</div>
			{/if}

			<!-- Node Type Badge -->
			<div class="mt-3 flex justify-center">
				<Badge variant="secondary" class="text-xs">
					{category === 'basic' ? 'Required' :
					 category === 'final' ? 'Final Step' : 'Filter'}
				</Badge>
			</div>
		</CardContent>
	</Card>
</div>

<!-- Node Edit Dialog -->
<NodeEditDialog
	bind:open={dialogOpen}
	bind:nodeData={nodeDataForDialog}
	onSave={handleSaveNode}
	onModalClose={handleModalClose}
	getNodeColor={() => color}
/>

<style>
	.node-group-component {
		transition: all 0.2s ease;
	}

	.node-group-component:hover {
		transform: translateY(-2px);
		box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
	}
</style>
